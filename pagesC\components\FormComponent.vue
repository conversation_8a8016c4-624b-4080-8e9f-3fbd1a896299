<template>
  <view class="form">
    <!-- 表单头部 -->
    <view class="form-header">
      <view class="form-header-content">
        <view class="form-icon"></view>
        <view class="form-title-wrapper">
          <text class="form-title">个人定制训练计划</text>
          <text class="form-subtitle">请根据你的实际情况进行填写</text>
        </view>
      </view>
      <!-- 进度指示器 -->
      <view class="progress">
        <text class="progress-text progress-current">{{ currentStep }}</text>
        <text class="progress-text progress-separator">/</text>
        <text class="progress-text progress-total">{{ totalSteps }}</text>
      </view>
    </view>

    <!-- 问题1：运动目标 -->
    <text class="question-title">1.您的运动目标是什么？</text>
    <view
      v-for="(option, index) in goalOptions"
      :key="'goal-' + index"
      :class="['option-btn', { 'selected': selectedOptions.goal === option.value }]"
      @tap="handleOptionClick('goal', option)"
    >
      <text :class="['option-text', { 'selected': selectedOptions.goal === option.value }]">
        {{ option.text }}
      </text>
    </view>

    <!-- 问题2：运动经验 -->
    <text v-if="currentStep >= 2" class="question-title">2.您的运动经验如何？</text>
    <view
      v-if="currentStep >= 2"
      v-for="(option, index) in experienceOptions"
      :key="'experience-' + index"
      :class="['option-btn', { 'selected': selectedOptions.experience === option.value }]"
      @tap="handleOptionClick('experience', option)"
    >
      <text :class="['option-text', { 'selected': selectedOptions.experience === option.value }]">
        {{ option.text }}
      </text>
    </view>

    <!-- 问题3：运动频次 -->
    <text v-if="currentStep >= 3" class="question-title">3.每周可运动时间？</text>
    <view
      v-if="currentStep >= 3"
      v-for="(option, index) in timeOptions"
      :key="'time-' + index"
      :class="['option-btn', { 'selected': selectedOptions.time === option.value }]"
      @tap="handleOptionClick('time', option)"
    >
      <text :class="['option-text', { 'selected': selectedOptions.time === option.value }]">
        {{ option.text }}
      </text>
    </view>

    <!-- 生成按钮 -->
    <view
      v-if="currentStep === 3 && selectedOptions.time"
      class="submit-btn"
      @tap="handleSubmit"
    >
      <text class="submit-text">生成我的运动计划</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'FormComponent',
  data() {
    return {
      currentStep: 1,
      totalSteps: 3,
      selectedOptions: {},

      // 运动目标选项
      goalOptions: [
        { value: 'weight-loss', text: '减肥瘦身' },
        { value: 'muscle-gain', text: '增肌塑形' },
        { value: 'endurance', text: '提高体能' },
        { value: 'health', text: '保持健康' }
      ],

      // 运动经验选项
      experienceOptions: [
        { value: 'beginner', text: '初学者（很少运动）' },
        { value: 'intermediate', text: '有一定基础（偶尔运动）' },
        { value: 'advanced', text: '经验丰富（经常运动）' }
      ],

      // 运动时间选项
      timeOptions: [
        { value: '1-2', text: '1-2次' },
        { value: '3-4', text: '3-4次' },
        { value: '5+', text: '5次以上' }
      ]
    }
  },

  
  methods: {
    // 处理选项点击
    handleOptionClick(category, option) {
      console.log('2025-01-26 10:00:00,001-INFO-[FormComponent][handleOptionClick_001] 选项点击:', category, option);

      // 保存选择
      this.$set(this.selectedOptions, category, option.value);

      // 自动进入下一步
      setTimeout(() => {
        this.showNextStep(category);
      }, 300);
    },
    
    // 显示下一步
    showNextStep(category) {
      console.log('2025-01-26 10:00:00,002-INFO-[FormComponent][showNextStep_001] 显示下一步:', category);

      if (category === 'goal' && this.currentStep === 1) {
        this.currentStep = 2;
      } else if (category === 'experience' && this.currentStep === 2) {
        this.currentStep = 3;
      }

      // 滚动到最新位置
      this.$nextTick(() => {
        this.scrollToBottom();
      });
    },

    // 处理提交
    handleSubmit() {
      if (!this.selectedOptions.goal || !this.selectedOptions.experience || !this.selectedOptions.time) {
        uni.showToast({
          title: '请完成所有选择',
          icon: 'none'
        });
        return;
      }

      this.completeForm();
    },
    
    // 完成表单
    completeForm() {
      console.log('2025-01-26 10:00:00,003-INFO-[FormComponent][completeForm_001] 表单完成:', this.selectedOptions);

      // 生成个性化运动计划
      const plan = this.generatePersonalizedPlan(this.selectedOptions);

      // 向父组件发送完成事件
      this.$emit('form-complete', {
        selectedOptions: this.selectedOptions,
        plan: plan
      });
    },

    // 生成个性化运动计划
    generatePersonalizedPlan(options) {
      const { goal, experience, time } = options;

      let plan = ['根据您的选择，为您制定以下运动计划：'];

      // 根据目标添加建议
      switch(goal) {
        case 'weight-loss':
          plan.push('🎯 目标：减肥瘦身');
          plan.push('💪 建议：有氧运动为主，配合力量训练');
          plan.push('🏃‍♀️ 推荐：跑步、游泳、骑行');
          break;
        case 'muscle-gain':
          plan.push('🎯 目标：增肌塑形');
          plan.push('💪 建议：力量训练为主，适量有氧');
          plan.push('🏋️‍♀️ 推荐：器械训练、自重训练');
          break;
        case 'endurance':
          plan.push('🎯 目标：提高体能');
          plan.push('💪 建议：有氧和无氧结合训练');
          plan.push('🏃‍♀️ 推荐：间歇训练、功能性训练');
          break;
        case 'health':
          plan.push('🎯 目标：保持健康');
          plan.push('💪 建议：适度运动，注重持续性');
          plan.push('🚶‍♀️ 推荐：快走、瑜伽、太极');
          break;
      }

      // 根据经验调整强度
      switch(experience) {
        case 'beginner':
          plan.push('📈 强度：从低强度开始，循序渐进');
          break;
        case 'intermediate':
          plan.push('📈 强度：中等强度，稳步提升');
          break;
        case 'advanced':
          plan.push('📈 强度：可进行高强度训练');
          break;
      }

      // 根据时间安排频次
      switch(time) {
        case '1-2':
          plan.push('⏰ 频次：每周1-2次，每次45-60分钟');
          break;
        case '3-4':
          plan.push('⏰ 频次：每周3-4次，每次30-45分钟');
          break;
        case '5+':
          plan.push('⏰ 频次：每周5次以上，每次30分钟');
          break;
      }

      plan.push('💡 记住：坚持比强度更重要，循序渐进是关键！');

      return plan;
    },

    // 滚动到底部
    scrollToBottom() {
      setTimeout(() => {
        uni.pageScrollTo({
          scrollTop: 99999,
          duration: 300
        });
      }, 100);
    }
  }
}
</script>

<style scoped>
/* 表单区域 - Bento Grid风格 */
.form {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 32rpx;
  width: 100%;
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  padding: 0;
  box-sizing: border-box;
  margin-bottom: 40rpx;
  backdrop-filter: blur(20rpx);
  box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.08), 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
}

/* 表单头部 - Bento Grid风格 */
.form-header {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.95) 100%);
  border-radius: 32rpx 32rpx 0 0;
  width: 100%;
  padding: 36rpx 48rpx;
  border: none;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.06);
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
}

.form-header-content {
  display: flex;
  align-items: center;
  gap: 20rpx;
  flex: 1;
}

.form-icon {
  width: 32rpx;
  height: 32rpx;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 50%;
  flex-shrink: 0;
}

.form-title-wrapper {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.form-title {
  color: rgba(20, 20, 30, 0.95);
  font-size: 32rpx;
  letter-spacing: -0.2rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
  font-weight: 600;
  line-height: 40rpx;
}

.form-subtitle {
  color: rgba(100, 100, 120, 0.8);
  font-size: 26rpx;
  letter-spacing: 0.1rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
  font-weight: 400;
  line-height: 32rpx;
}

/* 进度指示器 */
.progress {
  background-color: rgba(239, 236, 253, 1);
  border-radius: 20rpx;
  width: 76rpx;
  height: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2rpx;
  flex-shrink: 0;
}

.progress-text {
  font-size: 22rpx;
  letter-spacing: 0rpx;
  font-family: Microsoft YaHei UI-Bold, Microsoft YaHei, sans-serif;
  font-weight: 600;
  line-height: 22rpx;
}

.progress-current {
  color: rgba(93, 89, 210, 1);
}

.progress-separator {
  color: rgba(171, 179, 198, 1);
  font-size: 20rpx;
  font-weight: 500;
}

.progress-total {
  color: rgba(21, 18, 62, 1);
}

/* 问题标题 - Bento Grid风格 */
.question-title {
  color: rgba(20, 20, 30, 0.95);
  font-size: 34rpx;
  letter-spacing: -0.1rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
  font-weight: 600;
  line-height: 44rpx;
  margin: 48rpx 48rpx 36rpx;
  display: block;
}

/* 选项按钮 - Bento Grid风格 */
.option-btn {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  height: 104rpx;
  width: calc(100% - 96rpx);
  margin: 20rpx 48rpx;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1rpx solid rgba(0, 0, 0, 0.06);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04), 0 1rpx 4rpx rgba(0, 0, 0, 0.02);
  backdrop-filter: blur(10rpx);
}

.option-btn:hover {
  background: rgba(248, 250, 252, 0.98);
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08), 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  border-color: rgba(99, 102, 239, 0.2);
}

.option-btn.selected {
  background: linear-gradient(135deg, rgba(99, 102, 239, 0.08) 0%, rgba(139, 92, 246, 0.06) 100%);
  border: 2rpx solid rgba(99, 102, 239, 0.6);
  box-shadow: 0 8rpx 32rpx rgba(99, 102, 239, 0.15), 0 2rpx 8rpx rgba(99, 102, 239, 0.1);
}

.option-text {
  color: rgba(30, 30, 40, 0.9);
  font-size: 30rpx;
  letter-spacing: 0.1rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
  font-weight: 500;
  line-height: 36rpx;
}

.option-text.selected {
  color: rgba(99, 102, 239, 0.9);
  font-weight: 600;
}

/* 确认按钮 - Bento Grid风格 */
.submit-btn {
  background: linear-gradient(135deg, rgba(99, 102, 239, 1) 0%, rgba(139, 92, 246, 1) 100%);
  border-radius: 20rpx;
  height: 112rpx;
  width: calc(100% - 96rpx);
  margin: 40rpx 48rpx 60rpx;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  box-shadow: 0 12rpx 48rpx rgba(99, 102, 239, 0.25), 0 4rpx 16rpx rgba(99, 102, 239, 0.15);
}

.submit-btn:hover {
  background: linear-gradient(135deg, rgba(79, 82, 219, 1) 0%, rgba(119, 72, 226, 1) 100%);
  transform: translateY(-4rpx);
  box-shadow: 0 16rpx 64rpx rgba(99, 102, 239, 0.3), 0 6rpx 20rpx rgba(99, 102, 239, 0.2);
}

.submit-text {
  color: rgba(255, 255, 255, 0.95);
  font-size: 32rpx;
  letter-spacing: 0.2rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
  font-weight: 600;
  line-height: 36rpx;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .form {
    border-radius: 32rpx;
  }
  
  .form-header {
    border-radius: 32rpx 32rpx 0 0;
    padding: 24rpx 32rpx;
  }
  
  .question-title {
    font-size: 28rpx;
    margin: 32rpx 32rpx 24rpx;
  }
  
  .option-btn {
    width: calc(100% - 64rpx);
    margin: 12rpx 32rpx;
    height: 88rpx;
  }
  
  .submit-btn {
    width: calc(100% - 64rpx);
    margin: 24rpx 32rpx 40rpx;
    height: 88rpx;
  }
}
</style>
