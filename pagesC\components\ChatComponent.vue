<template>
  <scroll-view
    class="chat-section"
    id="chatSection"
    scroll-y
    :scroll-top="scrollTop"
    :scroll-with-animation="true"
    @scroll="onScroll"
  >
    <!-- 聊天消息列表 -->
    <view
      v-for="(message, index) in messages"
      :key="message.id || index"
      :class="['chat-message', message.type]"
    >
      <!-- 普通聊天消息 -->
      <view v-if="message.messageType === 'text'" :class="['chat-bubble', message.type === 'bot' ? 'bot' : '']">
        <!-- 正在输入指示器 -->
        <view v-if="message.isTyping" class="typing-dots">
          <text class="chat-text bot">
            <span class="typing-dots">
              <span></span>
              <span></span>
              <span></span>
            </span>
          </text>
        </view>

        <!-- 普通消息 -->
        <text
          v-else
          :class="['chat-text', message.type === 'bot' ? 'bot' : '']"
          :id="'message-' + (message.id || index)"
        >
          {{ displayText[message.id || index] || message.content }}
        </text>
      </view>

      <!-- 介绍组件消息 -->
      <view v-else-if="message.messageType === 'intro'" class="intro-message">
        <IntroComponent @question-click="handleQuestionClick" />
      </view>

      <!-- 表单组件消息 -->
      <view v-else-if="message.messageType === 'form'" class="form-message">
        <FormComponent
          @form-generating="handleFormGenerating"
          @form-complete="handleFormComplete"
        />
      </view>

      <!-- 商品推荐组件消息 -->
      <view v-else-if="message.messageType === 'product'" class="product-message">
        <ProductRecommendComponent
          @product-click="handleProductClick"
          @product-buy="handleProductBuy"
          @more-products="handleMoreProducts"
        />
      </view>
    </view>
  </scroll-view>
</template>

<script>
import IntroComponent from './IntroComponent.vue'
import FormComponent from './FormComponent.vue'
import ProductRecommendComponent from './ProductRecommendComponent.vue'

export default {
  name: 'ChatComponent',
  components: {
    IntroComponent,
    FormComponent,
    ProductRecommendComponent
  },
  props: {
    messages: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      displayText: {}, // 用于打字效果的显示文本
      typingTimeouts: {}, // 打字效果的定时器
      scrollTop: 0, // 滚动位置
      scrollHeight: 0 // 内容高度
    }
  },
  watch: {
    messages: {
      handler(newMessages, oldMessages) {
        // 检查是否有新消息需要打字效果
        if (newMessages.length > oldMessages.length) {
          const newMessage = newMessages[newMessages.length - 1];
          if (newMessage.type === 'bot' && !newMessage.isTyping) {
            this.startTypingEffect(newMessage);
          }
        }
        
        // 自动滚动到底部
        setTimeout(() => {
          this.scrollToBottom();
        }, 100);
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    // 开始打字效果
    startTypingEffect(message) {
      const messageId = message.id || this.messages.indexOf(message);
      const content = message.content;
      
      console.log('2025-01-26 10:00:00,001-INFO-[ChatComponent][startTypingEffect_001] 开始打字效果:', messageId);
      
      // 清除之前的定时器
      if (this.typingTimeouts[messageId]) {
        clearTimeout(this.typingTimeouts[messageId]);
      }
      
      // 初始化显示文本
      this.$set(this.displayText, messageId, '');
      
      let index = 0;
      const typeChar = () => {
        if (index < content.length) {
          this.$set(this.displayText, messageId, content.substring(0, index + 1));
          index++;
          this.typingTimeouts[messageId] = setTimeout(typeChar, 50);
          
          // 每次更新后滚动到底部
          setTimeout(() => {
            this.scrollToBottom();
          }, 50);
        } else {
          // 打字完成，清除定时器
          delete this.typingTimeouts[messageId];
        }
      };
      
      typeChar();
    },
    
    // 滚动到底部
    scrollToBottom() {
      this.$nextTick(() => {
        // 使用时间戳确保每次都能触发滚动
        const newScrollTop = Date.now() % 100000 + 99999;
        this.scrollTop = newScrollTop;
        console.log('2025-01-26 10:00:00,006-INFO-[ChatComponent][scrollToBottom_001] 滚动到底部:', newScrollTop);

        // 备用方案：如果scroll-view不工作，使用页面滚动
        setTimeout(() => {
          uni.pageScrollTo({
            scrollTop: 99999,
            duration: 300
          });
        }, 200);
      });
    },

    // 处理滚动事件
    onScroll(e) {
      // 可以在这里处理滚动事件，比如检测是否滚动到底部
      console.log('2025-01-26 10:00:00,007-INFO-[ChatComponent][onScroll_001] 滚动事件:', e.detail);
    },
    
    // 添加消息（供父组件调用）
    addMessage(message) {
      console.log('2025-01-26 10:00:00,002-INFO-[ChatComponent][addMessage_001] 添加消息:', message);
      this.$emit('add-message', message);
    },

    // 处理问题点击
    handleQuestionClick(question) {
      console.log('2025-01-26 10:00:00,003-INFO-[ChatComponent][handleQuestionClick_001] 问题点击:', question);
      this.$emit('question-click', question);
    },

    // 处理表单生成中
    handleFormGenerating() {
      console.log('2025-01-26 10:00:00,004-INFO-[ChatComponent][handleFormGenerating_001] 表单生成中');
      this.$emit('form-generating');
    },

    // 处理表单完成
    handleFormComplete(formData) {
      console.log('2025-01-26 10:00:00,005-INFO-[ChatComponent][handleFormComplete_001] 表单完成:', formData);
      this.$emit('form-complete', formData);
    },

    // 处理商品点击
    handleProductClick(product) {
      console.log('2025-01-26 10:00:00,006-INFO-[ChatComponent][handleProductClick_001] 商品点击:', product);
      this.$emit('product-click', product);
    },

    // 处理商品购买
    handleProductBuy(product) {
      console.log('2025-01-26 10:00:00,007-INFO-[ChatComponent][handleProductBuy_001] 商品购买:', product);
      this.$emit('product-buy', product);
    },

    // 处理查看更多商品
    handleMoreProducts() {
      console.log('2025-01-26 10:00:00,008-INFO-[ChatComponent][handleMoreProducts_001] 查看更多商品');
      this.$emit('more-products');
    }
  },
  
  beforeDestroy() {
    // 清除所有定时器
    Object.values(this.typingTimeouts).forEach(timeout => {
      clearTimeout(timeout);
    });
  }
}
</script>

<style scoped>
/* 聊天区域 */
.chat-section {
  width: 100%;
  padding: 0 40rpx 280rpx 40rpx;
  margin: 108rpx 0 0;
  height: calc(100vh - 388rpx);
  flex: 1;
}

/* 聊天消息 */
.chat-message {
  display: flex;
  margin-bottom: 24rpx;
}

.chat-message.user {
  justify-content: flex-end;
}

.chat-message.bot {
  justify-content: flex-start;
}

/* 聊天气泡 - Bento Grid风格 */
.chat-bubble {
  background: linear-gradient(135deg, rgba(95, 88, 255, 1) 0%, rgba(139, 92, 246, 1) 100%);
  border-radius: 24rpx;
  padding: 24rpx 32rpx;
  max-width: 480rpx;
  width: fit-content;
  box-shadow: 0 8rpx 32rpx rgba(95, 88, 255, 0.15), 0 2rpx 8rpx rgba(95, 88, 255, 0.1);
  word-wrap: break-word;
  border: 1rpx solid rgba(255, 255, 255, 0.1);
}

.chat-bubble.bot {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06), 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  max-width: 640rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10rpx);
}

/* 聊天文本 - Bento Grid风格 */
.chat-text {
  color: rgba(255, 255, 255, 0.95);
  font-size: 30rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  font-weight: 500;
  line-height: 42rpx;
  text-align: left;
  letter-spacing: 0.2rpx;
  word-wrap: break-word;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.chat-text.bot {
  color: rgba(30, 30, 30, 0.9);
  font-weight: 400;
  text-shadow: none;
}

/* 打字效果 */
.typing-dots {
  display: inline-flex;
  align-items: center;
  gap: 8rpx;
}

.typing-dots span {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background-color: rgba(120, 120, 120, 1);
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 介绍、表单和商品推荐消息样式 */
.intro-message,
.form-message,
.product-message {
  width: 100%;
  margin-bottom: 24rpx;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .chat-section {
    height: calc(100vh - 340rpx);
    margin: 100rpx 0 0;
    padding: 0 30rpx 260rpx 30rpx;
  }

  .chat-bubble {
    max-width: 400rpx;
    padding: 16rpx 24rpx;
  }

  .chat-bubble.bot {
    max-width: 520rpx;
  }

  .chat-text {
    font-size: 26rpx;
    line-height: 34rpx;
  }
}

@media (max-width: 480px) {
  .chat-section {
    height: calc(100vh - 312rpx);
    margin: 92rpx 0 0;
    padding: 0 24rpx 240rpx 24rpx;
  }
  
  .chat-bubble {
    max-width: 320rpx;
    padding: 14rpx 20rpx;
    border-radius: 28rpx 6rpx 28rpx 28rpx;
  }
  
  .chat-bubble.bot {
    max-width: 400rpx;
    border-radius: 6rpx 28rpx 28rpx 28rpx;
  }
  
  .chat-text {
    font-size: 24rpx;
    line-height: 32rpx;
  }
  
  .typing-dots span {
    width: 10rpx;
    height: 10rpx;
  }
}
</style>
