# 运动健康小助手后端接口方案

## 📋 项目概述

本文档为运动健康小助手聊天页面提供完整的后端接口开发方案，包括数据库设计、API接口、后台管理等功能模块。

## 🎯 功能模块

- **聊天消息管理** - 用户与AI助手的对话记录
- **运动计划生成** - 个性化运动方案制定
- **健康评估系统** - 肩颈等健康状况评估
- **商品推荐引擎** - 基于用户需求的商品推荐
- **用户健康档案** - 个人健康数据管理

## 📊 数据库设计

### 1. 聊天消息记录表 (ddwx_chat_messages)

```sql
CREATE TABLE `ddwx_chat_messages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `aid` int(11) NOT NULL DEFAULT '0' COMMENT '应用ID',
  `mid` int(11) NOT NULL DEFAULT '0' COMMENT '用户ID',
  `message_type` varchar(20) NOT NULL DEFAULT 'text' COMMENT '消息类型：user/bot/intro/form/product',
  `content` text COMMENT '消息内容',
  `extra_data` text COMMENT '额外数据JSON格式',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1正常 0删除',
  PRIMARY KEY (`id`),
  KEY `aid_mid` (`aid`,`mid`),
  KEY `create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='聊天消息记录表';
```

### 2. 运动计划表 (ddwx_exercise_plans)

```sql
CREATE TABLE `ddwx_exercise_plans` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `aid` int(11) NOT NULL DEFAULT '0' COMMENT '应用ID',
  `mid` int(11) NOT NULL DEFAULT '0' COMMENT '用户ID',
  `goal` varchar(50) NOT NULL DEFAULT '' COMMENT '运动目标',
  `experience` varchar(50) NOT NULL DEFAULT '' COMMENT '运动经验',
  `time_available` varchar(50) NOT NULL DEFAULT '' COMMENT '可用时间',
  `plan_content` text COMMENT '计划内容JSON格式',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1正常 0删除',
  PRIMARY KEY (`id`),
  KEY `aid_mid` (`aid`,`mid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='运动计划表';
```

### 3. 健康评估表 (ddwx_health_assessments)

```sql
CREATE TABLE `ddwx_health_assessments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `aid` int(11) NOT NULL DEFAULT '0' COMMENT '应用ID',
  `mid` int(11) NOT NULL DEFAULT '0' COMMENT '用户ID',
  `assessment_type` varchar(50) NOT NULL DEFAULT '' COMMENT '评估类型：neck/shoulder等',
  `questions` text COMMENT '问题内容JSON格式',
  `answers` text COMMENT '回答内容JSON格式',
  `result` text COMMENT '评估结果JSON格式',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1正常 0删除',
  PRIMARY KEY (`id`),
  KEY `aid_mid` (`aid`,`mid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='健康评估表';
```

### 4. 商品推荐记录表 (ddwx_product_recommendations)

```sql
CREATE TABLE `ddwx_product_recommendations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `aid` int(11) NOT NULL DEFAULT '0' COMMENT '应用ID',
  `mid` int(11) NOT NULL DEFAULT '0' COMMENT '用户ID',
  `product_ids` text COMMENT '推荐商品ID列表JSON格式',
  `recommendation_reason` varchar(255) DEFAULT '' COMMENT '推荐理由',
  `click_count` int(11) NOT NULL DEFAULT '0' COMMENT '点击次数',
  `purchase_count` int(11) NOT NULL DEFAULT '0' COMMENT '购买次数',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1正常 0删除',
  PRIMARY KEY (`id`),
  KEY `aid_mid` (`aid`,`mid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品推荐记录表';
```

### 5. 用户健康档案表 (ddwx_user_health_profiles)

```sql
CREATE TABLE `ddwx_user_health_profiles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `aid` int(11) NOT NULL DEFAULT '0' COMMENT '应用ID',
  `mid` int(11) NOT NULL DEFAULT '0' COMMENT '用户ID',
  `age` int(3) DEFAULT '0' COMMENT '年龄',
  `gender` tinyint(1) DEFAULT '0' COMMENT '性别：1男 2女',
  `height` decimal(5,2) DEFAULT '0.00' COMMENT '身高cm',
  `weight` decimal(5,2) DEFAULT '0.00' COMMENT '体重kg',
  `health_goals` text COMMENT '健康目标JSON格式',
  `exercise_preferences` text COMMENT '运动偏好JSON格式',
  `health_conditions` text COMMENT '健康状况JSON格式',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `aid_mid` (`aid`,`mid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户健康档案表';
```

## 🔧 API接口设计

### 前端接口文件：app/controller/ApiHealth.php

```php
<?php
namespace app\controller;
use app\common\ApiCommon;

class ApiHealth extends ApiCommon
{
    // 聊天相关接口
    public function sendMessage()        // 发送聊天消息
    public function getChatHistory()     // 获取聊天历史
    
    // 运动计划相关接口
    public function generateExercisePlan()  // 生成运动计划
    public function getExercisePlans()      // 获取历史运动计划
    public function saveExercisePlan()      // 保存运动计划
    
    // 健康评估相关接口
    public function startAssessment()       // 开始评估
    public function submitAssessment()      // 提交评估结果
    public function getAssessmentHistory()  // 获取评估历史
    
    // 商品推荐相关接口
    public function getProductRecommendations()  // 获取商品推荐
    public function recordProductClick()         // 记录商品点击
    public function recordProductPurchase()     // 记录商品购买
}
```

### 接口详细说明

#### 1. 发送聊天消息
- **接口地址**：`POST /ApiHealth/sendMessage`
- **请求参数**：
  ```json
  {
    "message_type": "text",
    "content": "消息内容",
    "extra_data": {}
  }
  ```
- **返回格式**：
  ```json
  {
    "code": 1,
    "msg": "发送成功",
    "data": {
      "message_id": 123
    }
  }
  ```

#### 2. 生成运动计划
- **接口地址**：`POST /ApiHealth/generateExercisePlan`
- **请求参数**：
  ```json
  {
    "goal": "weight-loss",
    "experience": "beginner",
    "time_available": "3-4"
  }
  ```
- **返回格式**：
  ```json
  {
    "code": 1,
    "msg": "生成成功",
    "data": {
      "plan_id": 456,
      "plan": [
        "🎯 目标：减肥瘦身",
        "💪 建议：有氧运动为主，配合力量训练",
        "🏃‍♀️ 推荐：跑步、游泳、骑行"
      ]
    }
  }
  ```

#### 3. 获取商品推荐
- **接口地址**：`GET /ApiHealth/getProductRecommendations`
- **请求参数**：
  ```json
  {
    "user_profile": {}
  }
  ```
- **返回格式**：
  ```json
  {
    "code": 1,
    "msg": "获取成功",
    "data": [
      {
        "id": 1,
        "name": "蛋白粉 乳清蛋白",
        "description": "高品质乳清蛋白，助力肌肉增长",
        "price": "168.00",
        "original_price": "228.00",
        "rating": 4.8,
        "image": "/static/img/product-protein.png",
        "badge": "热销"
      }
    ]
  }
  ```

#### 4. 开始健康评估
- **接口地址**：`POST /ApiHealth/startAssessment`
- **请求参数**：
  ```json
  {
    "assessment_type": "neck"
  }
  ```
- **返回格式**：
  ```json
  {
    "code": 1,
    "msg": "获取成功",
    "data": {
      "questions": [
        "您是否经常感到颈部僵硬？",
        "您是否有头痛的症状？",
        "您每天使用电脑的时间是多久？"
      ]
    }
  }
  ```

#### 5. 提交评估结果
- **接口地址**：`POST /ApiHealth/submitAssessment`
- **请求参数**：
  ```json
  {
    "assessment_type": "neck",
    "answers": ["偶尔", "轻微", "4-8小时"]
  }
  ```
- **返回格式**：
  ```json
  {
    "code": 1,
    "msg": "提交成功",
    "data": {
      "assessment_id": 789,
      "result": {
        "score": 3,
        "level": "轻度风险",
        "suggestion": "建议您注意颈部保健，适当进行颈部运动。"
      }
    }
  }
  ```

## 🔧 后台管理设计

### 后台管理文件：app/controller/Health.php

```php
<?php
namespace app\controller;
use app\common\Common;

class Health extends Common
{
    public function index()           // 聊天记录列表
    public function planList()        // 运动计划列表
    public function assessmentList()  // 健康评估列表
    public function productStats()    // 商品推荐统计
    public function deleteChatMessage() // 删除聊天记录
    public function exportData()      // 导出数据
}
```

### 菜单配置 (app/common/Menu.php)

```php
[
    'name' => '运动健康助手',
    'icon' => 'fa fa-heartbeat',
    'sub' => [
        [
            'name' => '聊天记录',
            'url' => 'Health/index',
            'icon' => 'fa fa-comments'
        ],
        [
            'name' => '运动计划',
            'url' => 'Health/planList',
            'icon' => 'fa fa-dumbbell'
        ],
        [
            'name' => '健康评估',
            'url' => 'Health/assessmentList',
            'icon' => 'fa fa-stethoscope'
        ],
        [
            'name' => '商品推荐',
            'url' => 'Health/productStats',
            'icon' => 'fa fa-shopping-cart'
        ]
    ]
]
```

## 📱 前端接口调用

### JavaScript调用示例

```javascript
// 发送消息
sendMessage(messageData) {
  return this.$http.post('/ApiHealth/sendMessage', messageData);
}

// 生成运动计划
generateExercisePlan(planData) {
  return this.$http.post('/ApiHealth/generateExercisePlan', planData);
}

// 获取商品推荐
getProductRecommendations() {
  return this.$http.get('/ApiHealth/getProductRecommendations');
}

// 记录商品点击
recordProductClick(productId) {
  return this.$http.post('/ApiHealth/recordProductClick', {
    product_id: productId
  });
}

// 开始健康评估
startAssessment(type) {
  return this.$http.post('/ApiHealth/startAssessment', {
    assessment_type: type
  });
}

// 提交评估结果
submitAssessment(type, answers) {
  return this.$http.post('/ApiHealth/submitAssessment', {
    assessment_type: type,
    answers: answers
  });
}
```

## 🚀 实施步骤

### 第一阶段：数据库准备
1. **创建SQL文件** - `health_assistant.sql`
2. **执行数据库脚本** - 创建所有必要的数据表
3. **验证表结构** - 确保所有字段和索引正确

### 第二阶段：后端开发
1. **创建API接口文件** - `app/controller/ApiHealth.php`
2. **创建后台管理文件** - `app/controller/Health.php`
3. **更新菜单配置** - `app/common/Menu.php`
4. **创建HTML模板文件** - 对应的视图文件

### 第三阶段：前端适配
1. **更新接口调用** - 适配新的API接口
2. **测试功能模块** - 确保所有功能正常工作
3. **优化用户体验** - 根据实际使用情况调整

### 第四阶段：测试部署
1. **单元测试** - 测试各个接口功能
2. **集成测试** - 测试前后端交互
3. **性能测试** - 确保系统性能
4. **部署上线** - 正式环境部署

## 📝 注意事项

### 开发规范
- 所有接口都要继承正确的基类 (`ApiCommon` 或 `Common`)
- 返回格式要统一 (`code`, `msg`, `data`)
- 要添加用户权限验证和登录状态检查
- 要记录重要操作日志 (`System::plog()`)
- 数据库操作要使用事务处理
- 要处理异常情况和错误返回

### 安全考虑
- 输入参数验证和过滤
- SQL注入防护
- XSS攻击防护
- 用户权限验证
- 敏感数据加密存储

### 性能优化
- 数据库查询优化
- 适当使用缓存机制
- 分页查询处理
- 索引优化设计

## 📊 数据流程图

```
用户操作 → 前端页面 → API接口 → 数据库操作 → 返回结果 → 前端展示
    ↓
聊天记录存储 → 后台管理查看 → 数据分析统计
```

## 🎯 扩展功能

### 未来可扩展的功能模块
- **AI智能回复** - 集成GPT等AI模型
- **语音交互** - 支持语音输入和输出
- **图片识别** - 运动姿势识别和纠正
- **数据分析** - 用户行为分析和报告
- **社交功能** - 用户间的经验分享
- **第三方集成** - 健康设备数据同步

---

**文档版本**：v1.0  
**创建时间**：2025-01-26  
**更新时间**：2025-01-26  
**维护人员**：开发团队
