<template>
  <view class="intro">
    <!-- 介绍文本 -->
    <view class="intro-header">
      <text class="intro-title">你好，我是你的运动健康小助手</text>
      <text class="intro-desc">不知道如何训练，来问一问，帮你制定专属训练计划，伴你科学运动，避免运动误区</text>
    </view>

    <!-- 常见问题列表 -->
    <view class="questions">
      <view 
        v-for="(question, index) in questionList" 
        :key="index"
        class="question-item"
        @tap="handleQuestionClick(question)"
      >
        <view class="question-content">
          <image
            class="question-icon"
            :src="pre_url + '/static/img/FigmaDDSSlicePNG01c5ff2249a1931fb769f5fe308e677a.png'"
            mode="aspectFit"
          />
          <text class="question-text">{{ question.text }}</text>
        </view>
        <image
          class="arrow-icon"
          :src="pre_url + '/static/img/FigmaDDSSlicePNGbfc9395ec208fb6e3eadc99506d91c4d.png'"
          mode="aspectFit"
        />
      </view>
    </view>
  </view>
</template>

<script>
var app = getApp();

export default {
  name: 'IntroComponent',
  data() {
    return {
      pre_url: app.globalData.pre_url,
      questionList: [
        {
          id: 1,
          text: '减肥的误区有什么？',
          category: 'weight-loss'
        },
        {
          id: 2,
          text: '如何制定科学的运动计划？',
          category: 'exercise-plan'
        },
        {
          id: 3,
          text: '运动后如何正确恢复？',
          category: 'recovery'
        }
      ]
    }
  },
  methods: {
    // 处理问题点击
    handleQuestionClick(question) {
      console.log('2025-01-26 10:00:00,001-INFO-[IntroComponent][handleQuestionClick_001] 问题点击:', question);
      
      // 添加点击反馈效果
      const clickedElement = event.currentTarget;
      clickedElement.style.transform = 'scale(0.98)';
      setTimeout(() => {
        clickedElement.style.transform = '';
      }, 150);
      
      // 向父组件发送问题点击事件
      this.$emit('question-click', question.text);
    }
  }
}
</script>

<style scoped>
/* 介绍区域 - Bento Grid风格 */
.intro {
  background: rgba(255, 255, 255, 0.9);
  width: 100%;
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  border-radius: 28rpx;
  padding: 48rpx;
  box-sizing: border-box;
  margin-bottom: 40rpx;
  backdrop-filter: blur(20rpx);
  box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.08), 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
}

/* 介绍头部 */
.intro-header {
  margin-bottom: 40rpx;
  text-align: center;
}

.intro-title {
  color: rgba(20, 20, 30, 0.95);
  font-size: 36rpx;
  letter-spacing: -0.2rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
  font-weight: 600;
  line-height: 48rpx;
  margin-bottom: 20rpx;
  display: block;
  text-align: center;
}

.intro-desc {
  color: rgba(100, 100, 120, 0.8);
  font-size: 26rpx;
  letter-spacing: 0.2rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
  font-weight: 400;
  text-align: center;
  line-height: 38rpx;
  display: block;
}

/* 问题列表 */
.questions {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.question-item {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  width: 100%;
  height: 104rpx;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
  box-sizing: border-box;
  border: 1rpx solid rgba(0, 0, 0, 0.06);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04), 0 1rpx 4rpx rgba(0, 0, 0, 0.02);
  backdrop-filter: blur(10rpx);
}

.question-item:hover {
  background: rgba(248, 250, 252, 0.98);
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08), 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  border-color: rgba(99, 102, 239, 0.2);
}

.question-item:active {
  transform: translateY(-2rpx) scale(0.98);
}

/* 问题内容 */
.question-content {
  display: flex;
  align-items: center;
  gap: 20rpx;
  flex: 1;
}

.question-icon {
  width: 28rpx;
  height: 28rpx;
  flex-shrink: 0;
}

.question-text {
  color: rgba(30, 30, 40, 0.9);
  font-size: 30rpx;
  letter-spacing: 0.1rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
  font-weight: 500;
  line-height: 36rpx;
  flex: 1;
}

/* 箭头图标 */
.arrow-icon {
  width: 16rpx;
  height: 24rpx;
  flex-shrink: 0;
  opacity: 0.6;
  transition: all 0.2s ease;
}

.question-item:hover .arrow-icon {
  opacity: 1;
  transform: translateX(4rpx);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .intro {
    padding: 32rpx;
    margin-bottom: 32rpx;
    border-radius: 28rpx;
  }
  
  .intro-title {
    font-size: 28rpx;
    line-height: 36rpx;
  }
  
  .intro-desc {
    font-size: 22rpx;
    line-height: 28rpx;
  }
  
  .question-item {
    height: 88rpx;
    padding: 0 24rpx;
    border-radius: 44rpx;
  }
  
  .question-text {
    font-size: 26rpx;
  }
  
  .question-icon {
    width: 24rpx;
    height: 24rpx;
  }
}

@media (max-width: 480px) {
  .intro {
    padding: 24rpx;
    margin-bottom: 24rpx;
    border-radius: 24rpx;
  }
  
  .intro-header {
    margin-bottom: 32rpx;
  }
  
  .intro-title {
    font-size: 26rpx;
    line-height: 34rpx;
    margin-bottom: 12rpx;
  }
  
  .intro-desc {
    font-size: 20rpx;
    line-height: 26rpx;
  }
  
  .questions {
    gap: 12rpx;
  }
  
  .question-item {
    height: 80rpx;
    padding: 0 20rpx;
    border-radius: 40rpx;
  }
  
  .question-content {
    gap: 16rpx;
  }
  
  .question-text {
    font-size: 24rpx;
    line-height: 24rpx;
  }
  
  .question-icon {
    width: 20rpx;
    height: 20rpx;
  }
  
  .arrow-icon {
    width: 12rpx;
    height: 20rpx;
  }
}
</style>
