<template>
  <view class="product-recommend">
    <!-- 推荐标题 -->
    <view class="recommend-header">
      <text class="recommend-title">🛍️ 为您推荐</text>
      <text class="recommend-subtitle">根据您的健康需求精选商品</text>
    </view>

    <!-- 商品列表 -->
    <view class="product-list">
      <view 
        v-for="(product, index) in productList" 
        :key="product.id"
        class="product-card"
        @tap="handleProductClick(product)"
      >
        <!-- 商品图片 -->
        <view class="product-image-wrapper">
          <image 
            class="product-image"
            :src="pre_url + product.image"
            mode="aspectFill"
          />
          <view class="product-badge" v-if="product.badge">
            <text class="badge-text">{{ product.badge }}</text>
          </view>
        </view>

        <!-- 商品信息 -->
        <view class="product-info">
          <text class="product-name">{{ product.name }}</text>
          <text class="product-desc">{{ product.description }}</text>
          
          <!-- 价格和评分 -->
          <view class="product-meta">
            <view class="price-wrapper">
              <text class="price-symbol">¥</text>
              <text class="price-current">{{ product.price }}</text>
              <text class="price-original" v-if="product.originalPrice">¥{{ product.originalPrice }}</text>
            </view>
            <view class="rating-wrapper">
              <text class="rating-stars">{{ getStars(product.rating) }}</text>
              <text class="rating-text">{{ product.rating }}</text>
            </view>
          </view>

          <!-- 购买按钮 -->
          <view class="buy-btn" @tap.stop="handleBuyClick(product)">
            <text class="buy-text">立即购买</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 查看更多 -->
    <view class="more-btn" @tap="handleMoreClick">
      <text class="more-text">查看更多商品</text>
      <text class="more-arrow">→</text>
    </view>
  </view>
</template>

<script>
var app = getApp();

export default {
  name: 'ProductRecommendComponent',
  data() {
    return {
      pre_url: app.globalData.pre_url,
      
      // 模拟商品数据
      productList: [
        {
          id: 1,
          name: '蛋白粉 乳清蛋白',
          description: '高品质乳清蛋白，助力肌肉增长',
          price: '168.00',
          originalPrice: '228.00',
          rating: 4.8,
          image: '/static/img/product-protein.png',
          badge: '热销'
        },
        {
          id: 2,
          name: '运动健身套装',
          description: '专业运动装备，舒适透气',
          price: '299.00',
          originalPrice: '399.00',
          rating: 4.9,
          image: '/static/img/product-sportswear.png',
          badge: '推荐'
        },
        {
          id: 3,
          name: '智能体脂秤',
          description: '精准测量，健康管理好帮手',
          price: '89.00',
          originalPrice: '129.00',
          rating: 4.7,
          image: '/static/img/product-scale.png',
          badge: '新品'
        }
      ]
    }
  },
  
  methods: {
    // 获取星级显示
    getStars(rating) {
      const fullStars = Math.floor(rating);
      const hasHalfStar = rating % 1 >= 0.5;
      let stars = '★'.repeat(fullStars);
      if (hasHalfStar) stars += '☆';
      return stars;
    },
    
    // 处理商品点击
    handleProductClick(product) {
      console.log('2025-01-26 10:00:00,001-INFO-[ProductRecommendComponent][handleProductClick_001] 商品点击:', product);
      
      // 向父组件发送商品点击事件
      this.$emit('product-click', product);
    },
    
    // 处理购买点击
    handleBuyClick(product) {
      console.log('2025-01-26 10:00:00,002-INFO-[ProductRecommendComponent][handleBuyClick_001] 购买点击:', product);
      
      // 显示购买提示
      uni.showToast({
        title: '跳转到商品详情页',
        icon: 'none'
      });
      
      // 向父组件发送购买事件
      this.$emit('product-buy', product);
    },
    
    // 处理查看更多
    handleMoreClick() {
      console.log('2025-01-26 10:00:00,003-INFO-[ProductRecommendComponent][handleMoreClick_001] 查看更多');
      
      // 向父组件发送查看更多事件
      this.$emit('more-products');
    }
  }
}
</script>

<style scoped>
/* 商品推荐区域 - Bento Grid风格 */
.product-recommend {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 28rpx;
  width: 100%;
  padding: 48rpx;
  box-sizing: border-box;
  margin-bottom: 40rpx;
  backdrop-filter: blur(20rpx);
  box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.08), 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
}

/* 推荐头部 */
.recommend-header {
  margin-bottom: 40rpx;
  text-align: center;
}

.recommend-title {
  color: rgba(20, 20, 30, 0.95);
  font-size: 36rpx;
  letter-spacing: -0.2rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
  font-weight: 600;
  line-height: 48rpx;
  margin-bottom: 12rpx;
  display: block;
}

.recommend-subtitle {
  color: rgba(100, 100, 120, 0.8);
  font-size: 26rpx;
  letter-spacing: 0.2rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
  font-weight: 400;
  line-height: 36rpx;
  display: block;
}

/* 商品列表 */
.product-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

/* 商品卡片 */
.product-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 24rpx;
  display: flex;
  gap: 24rpx;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1rpx solid rgba(0, 0, 0, 0.06);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04), 0 1rpx 4rpx rgba(0, 0, 0, 0.02);
  backdrop-filter: blur(10rpx);
}

.product-card:hover {
  background: rgba(248, 250, 252, 0.98);
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08), 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  border-color: rgba(99, 102, 239, 0.2);
}

/* 商品图片 */
.product-image-wrapper {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  flex-shrink: 0;
}

.product-image {
  width: 100%;
  height: 100%;
  border-radius: 16rpx;
  background-color: rgba(248, 250, 252, 1);
}

.product-badge {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  background: linear-gradient(135deg, rgba(255, 107, 107, 1) 0%, rgba(255, 142, 83, 1) 100%);
  border-radius: 12rpx;
  padding: 4rpx 12rpx;
}

.badge-text {
  color: rgba(255, 255, 255, 0.95);
  font-size: 20rpx;
  font-weight: 600;
  line-height: 24rpx;
}

/* 商品信息 */
.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-name {
  color: rgba(20, 20, 30, 0.95);
  font-size: 32rpx;
  font-weight: 600;
  line-height: 40rpx;
  margin-bottom: 8rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
}

.product-desc {
  color: rgba(100, 100, 120, 0.8);
  font-size: 24rpx;
  font-weight: 400;
  line-height: 32rpx;
  margin-bottom: 16rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
}

/* 价格和评分 */
.product-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.price-wrapper {
  display: flex;
  align-items: baseline;
  gap: 4rpx;
}

.price-symbol {
  color: rgba(255, 107, 107, 1);
  font-size: 24rpx;
  font-weight: 600;
}

.price-current {
  color: rgba(255, 107, 107, 1);
  font-size: 36rpx;
  font-weight: 700;
  line-height: 36rpx;
}

.price-original {
  color: rgba(150, 150, 150, 0.8);
  font-size: 24rpx;
  font-weight: 400;
  text-decoration: line-through;
  margin-left: 8rpx;
}

.rating-wrapper {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.rating-stars {
  color: rgba(255, 193, 7, 1);
  font-size: 24rpx;
  line-height: 24rpx;
}

.rating-text {
  color: rgba(100, 100, 120, 0.8);
  font-size: 24rpx;
  font-weight: 500;
}

/* 购买按钮 */
.buy-btn {
  background: linear-gradient(135deg, rgba(99, 102, 239, 1) 0%, rgba(139, 92, 246, 1) 100%);
  border-radius: 16rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4rpx 16rpx rgba(99, 102, 239, 0.25);
}

.buy-btn:hover {
  background: linear-gradient(135deg, rgba(79, 82, 219, 1) 0%, rgba(119, 72, 226, 1) 100%);
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 20rpx rgba(99, 102, 239, 0.3);
}

.buy-text {
  color: rgba(255, 255, 255, 0.95);
  font-size: 26rpx;
  font-weight: 600;
  line-height: 26rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
}

/* 查看更多按钮 */
.more-btn {
  background: rgba(248, 250, 252, 0.8);
  border-radius: 16rpx;
  height: 80rpx;
  margin-top: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1rpx solid rgba(0, 0, 0, 0.06);
}

.more-btn:hover {
  background: rgba(240, 242, 247, 0.9);
  transform: translateY(-2rpx);
}

.more-text {
  color: rgba(100, 100, 120, 0.9);
  font-size: 28rpx;
  font-weight: 500;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
}

.more-arrow {
  color: rgba(100, 100, 120, 0.7);
  font-size: 24rpx;
  font-weight: 600;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .product-recommend {
    padding: 40rpx;
  }
  
  .product-card {
    padding: 20rpx;
    gap: 20rpx;
  }
  
  .product-image-wrapper {
    width: 140rpx;
    height: 140rpx;
  }
  
  .product-name {
    font-size: 28rpx;
  }
  
  .price-current {
    font-size: 32rpx;
  }
}

@media (max-width: 480px) {
  .product-recommend {
    padding: 32rpx;
  }
  
  .product-card {
    flex-direction: column;
    text-align: center;
  }
  
  .product-image-wrapper {
    width: 120rpx;
    height: 120rpx;
    align-self: center;
  }
  
  .product-meta {
    justify-content: center;
    gap: 32rpx;
  }
}
</style>
